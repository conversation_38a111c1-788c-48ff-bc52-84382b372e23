# Comprehensive Personal Details - HRMS Enhancement

## Overview
This enhancement adds extensive personal information fields to the HRMS employee profile system, providing a complete 360-degree view of employee information including education, work history, family details, certifications, and much more.

## Features Added

### 1. Enhanced Personal Information
- **Blood Group**: A+, A-, B+, B-, AB+, AB-, O+, O-
- **Religion**: Employee's religious affiliation
- **Caste**: Caste information (if applicable)
- **Category**: General, OBC, SC, ST, EWS
- **Physical Challenges**: Boolean flag for accessibility needs
- **Identification Marks**: Physical identification marks

### 2. Family Information
- **Father's Name**: Complete name of father
- **Mother's Name**: Complete name of mother
- **Spouse Details**: Name, occupation, phone number
- **Children Count**: Number of children
- **Family Members Table**: Detailed family member information with relationships

### 3. Address Information
#### Current Address
- Address Line 1 & 2
- City, State, Postal Code, Country

#### Permanent Address
- Separate permanent address fields
- Complete address with city, state, postal code, country

### 4. Contact Information
- **Personal Email**: Personal email address
- **Alternate Phone**: Secondary phone number
- **LinkedIn Profile**: Professional LinkedIn URL
- **Emergency Contact**: Name, phone, relationship

### 5. Bank & Identity Details
- **Bank Information**: Bank name, account number, IFSC code, branch
- **Government IDs**: 
  - PAN Number
  - Aadhar Number (masked display for security)
  - Passport Number & Expiry Date
  - Driving License Number & Expiry Date

### 6. Professional Details
- **Years of Experience**: Total professional experience
- **Previous Employment**: Company, designation, salary
- **Notice Period**: Notice period in days
- **Skills Arrays**: Technical skills, soft skills
- **Languages Known**: Array of known languages

### 7. Personal Interests
- **Hobbies**: Array of hobbies and interests
- **Interests**: Professional and personal interests
- **Achievements**: Notable achievements and awards

### 8. Health Information
- **Medical Conditions**: Any ongoing medical conditions
- **Allergies**: Known allergies and medical restrictions

## Database Tables Added

### 1. employee_education
Stores educational qualifications and academic history:
- Institution name, degree, field of study
- Start/end years, percentage/CGPA
- Board/university, location
- Completion status

### 2. employee_work_experience
Stores previous work experience:
- Company name, designation
- Start/end dates, current status
- Salary, location
- Job description, reason for leaving
- Supervisor contact information

### 3. employee_family_members
Stores family member information:
- Name, relationship, date of birth
- Occupation, contact details
- Dependent status, emergency contact flag

### 4. employee_certifications
Stores professional certifications:
- Certification name, issuing organization
- Issue/expiry dates
- Credential ID and URL
- Description

### 5. employee_languages
Stores language proficiency:
- Language name, proficiency level
- Reading, writing, speaking capabilities

### 6. employee_references
Stores professional and personal references:
- Name, designation, company
- Relationship type, contact details
- Years known

## API Methods Added

### Employee Service Extensions
```typescript
// Education Management
getEmployeeEducation(employeeId: string)
addEmployeeEducation(education: EmployeeEducation)
updateEmployeeEducation(id: string, education: Partial<EmployeeEducation>)
deleteEmployeeEducation(id: string)

// Work Experience Management
getEmployeeWorkExperience(employeeId: string)
addEmployeeWorkExperience(experience: EmployeeWorkExperience)
updateEmployeeWorkExperience(id: string, experience: Partial<EmployeeWorkExperience>)
deleteEmployeeWorkExperience(id: string)

// Family Members Management
getEmployeeFamilyMembers(employeeId: string)
addEmployeeFamilyMember(familyMember: EmployeeFamilyMember)
updateEmployeeFamilyMember(id: string, familyMember: Partial<EmployeeFamilyMember>)
deleteEmployeeFamilyMember(id: string)

// Certifications Management
getEmployeeCertifications(employeeId: string)
addEmployeeCertification(certification: EmployeeCertification)
updateEmployeeCertification(id: string, certification: Partial<EmployeeCertification>)
deleteEmployeeCertification(id: string)

// Languages Management
getEmployeeLanguages(employeeId: string)
addEmployeeLanguage(language: EmployeeLanguage)
updateEmployeeLanguage(id: string, language: Partial<EmployeeLanguage>)
deleteEmployeeLanguage(id: string)

// References Management
getEmployeeReferences(employeeId: string)
addEmployeeReference(reference: EmployeeReference)
updateEmployeeReference(id: string, reference: Partial<EmployeeReference>)
deleteEmployeeReference(id: string)

// Comprehensive Data Loading
getEmployeeWithComprehensiveDetails(employeeId: string)
```

## UI Components Enhanced

### EmployeeProfile Component
The main employee profile component now includes:

1. **Enhanced Personal Information Section**
   - All basic personal details with new fields
   - Blood group, religion, category selection
   - Family information display

2. **Address Information Section**
   - Side-by-side current and permanent address
   - Complete address forms with validation

3. **Bank & Identity Details Section**
   - Secure display of sensitive information
   - Masked account numbers and Aadhar for security

4. **Contact Information Section**
   - All contact methods in one place
   - LinkedIn profile integration
   - Emergency contact details

5. **Enhanced Job Information**
   - Existing job details with professional experience
   - Skills and certifications display

## Security Features

### Data Protection
- **Masked Display**: Sensitive information like bank account numbers and Aadhar numbers are masked
- **Secure Storage**: All personal data stored with proper encryption
- **Access Control**: Role-based access to sensitive information

### Privacy Considerations
- Optional fields for sensitive personal information
- Granular control over what information is displayed
- Audit trail for data modifications

## Migration Instructions

### Database Migration
1. Run the migration file: `06_comprehensive_personal_details.sql`
2. This will add all new columns and tables
3. Create necessary indexes for performance

### Frontend Updates
1. Updated TypeScript interfaces in `types/hrms.ts`
2. Enhanced service methods in `services/hrmsService.ts`
3. Comprehensive UI in `components/hrms/EmployeeProfile.tsx`

## Usage Examples

### Adding Education Record
```typescript
const education = {
  employee_id: 'emp-123',
  institution_name: 'University of Technology',
  degree: 'Bachelor of Engineering',
  field_of_study: 'Computer Science',
  start_year: 2018,
  end_year: 2022,
  percentage_or_cgpa: '8.5',
  is_completed: true
};

await hrmsService.addEmployeeEducation(education);
```

### Loading Comprehensive Profile
```typescript
const employee = await hrmsService.getEmployeeWithComprehensiveDetails(employeeId);
// Returns employee with all related data populated
```

## Benefits

1. **Complete Employee Records**: 360-degree view of employee information
2. **Better HR Management**: Comprehensive data for HR decisions
3. **Compliance**: Meets regulatory requirements for employee data
4. **User Experience**: Intuitive interface for data entry and viewing
5. **Scalability**: Modular design allows easy addition of new fields
6. **Security**: Proper handling of sensitive personal information

## Future Enhancements

1. **Document Attachments**: Link documents to specific sections
2. **Data Validation**: Enhanced validation rules for different fields
3. **Bulk Import**: Import comprehensive data from external sources
4. **Analytics**: Generate insights from comprehensive employee data
5. **Mobile Optimization**: Mobile-friendly interface for profile management

## Support

For any issues or questions regarding the comprehensive personal details feature, please refer to the HRMS documentation or contact the development team.
