import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  BarChart3, 
  Sitemap,
  RefreshCw,
  Download,
  UserPlus
} from 'lucide-react';
import { TeamMember } from '../../types/team';
import { 
  ViewMode, 
  SearchFilters, 
  TeamAnalytics, 
  EmployeeProfile,
  OrganizationNode 
} from '../../types/teamDirectory';
import { 
  fetchAllEmployees, 
  buildOrganizationChart, 
  searchEmployees, 
  getTeamAnalytics,
  getEmployeeProfile 
} from '../../services/teamDirectoryService';
import OrganizationalChart from './OrganizationalChart';
import EmployeeGrid from './EmployeeGrid';
import SearchAndFilters from './SearchAndFilters';
import TeamAnalyticsView from './TeamAnalytics';
import EmployeeProfileModal from './EmployeeProfileModal';
import ViewModeToggle from './ViewModeToggle';
import LoadingWithTimeout from '../LoadingWithTimeout';
import ErrorBoundary from '../ErrorBoundary';

const TeamDirectoryMain: React.FC = () => {
  const [viewMode, setViewMode] = useState<ViewMode>('chart');
  const [employees, setEmployees] = useState<TeamMember[]>([]);
  const [filteredEmployees, setFilteredEmployees] = useState<TeamMember[]>([]);
  const [organizationChart, setOrganizationChart] = useState<OrganizationNode[]>([]);
  const [analytics, setAnalytics] = useState<TeamAnalytics | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<EmployeeProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    searchQuery: '',
    departments: [],
    designations: [],
    locations: [],
    skills: [],
    hireDateRange: {},
    status: ['active']
  });

  // Load initial data
  useEffect(() => {
    loadTeamData();
  }, []);

  // Apply filters when they change
  useEffect(() => {
    applyFilters();
  }, [filters, employees]);

  const loadTeamData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const [employeesData, chartData, analyticsData] = await Promise.all([
        fetchAllEmployees(),
        buildOrganizationChart(),
        getTeamAnalytics()
      ]);

      console.log('🔍 Team Directory Data Loaded:');
      console.log('📊 Employees:', employeesData.length);
      console.log('🌳 Organization Chart:', chartData);
      console.log('📈 Analytics:', analyticsData);

      setEmployees(employeesData);
      setOrganizationChart(chartData);
      setAnalytics(analyticsData);
    } catch (err) {
      console.error('Error loading team data:', err);
      setError('Failed to load team directory data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = async () => {
    try {
      if (hasActiveFilters()) {
        const filtered = await searchEmployees(filters);
        setFilteredEmployees(filtered);
      } else {
        setFilteredEmployees(employees);
      }
    } catch (err) {
      console.error('Error applying filters:', err);
      setFilteredEmployees(employees);
    }
  };

  const hasActiveFilters = (): boolean => {
    return !!(
      filters.searchQuery ||
      filters.departments.length > 0 ||
      filters.designations.length > 0 ||
      filters.locations.length > 0 ||
      filters.skills.length > 0 ||
      filters.hireDateRange.start ||
      filters.hireDateRange.end
    );
  };

  const handleEmployeeClick = async (employee: TeamMember) => {
    if (!employee.id) return;
    
    try {
      const profile = await getEmployeeProfile(employee.id);
      setSelectedEmployee(profile);
    } catch (err) {
      console.error('Error loading employee profile:', err);
    }
  };

  const handleRefresh = () => {
    loadTeamData();
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log('Export functionality to be implemented');
  };

  const clearFilters = () => {
    setFilters({
      searchQuery: '',
      departments: [],
      designations: [],
      locations: [],
      skills: [],
      hireDateRange: {},
      status: ['active']
    });
  };

  const renderContent = () => {
    const dataToShow = hasActiveFilters() ? filteredEmployees : employees;

    console.log('🎯 renderContent - viewMode:', viewMode);
    console.log('🎯 renderContent - organizationChart:', organizationChart);
    console.log('🎯 renderContent - organizationChart.length:', organizationChart.length);

    switch (viewMode) {
      case 'chart':
        console.log('🎯 Rendering OrganizationalChart with nodes:', organizationChart);
        return (
          <OrganizationalChart
            nodes={organizationChart}
            onEmployeeClick={handleEmployeeClick}
            searchQuery={filters.searchQuery}
          />
        );
      case 'grid':
        return (
          <EmployeeGrid 
            employees={dataToShow}
            onEmployeeClick={handleEmployeeClick}
          />
        );
      case 'list':
        return (
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Designation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {dataToShow.map((employee) => (
                    <tr 
                      key={employee.id} 
                      className="hover:bg-gray-50 cursor-pointer"
                      onClick={() => handleEmployeeClick(employee)}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {employee.avatar ? (
                              <img 
                                className="h-10 w-10 rounded-full object-cover" 
                                src={employee.avatar} 
                                alt={employee.name}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  target.nextElementSibling?.classList.remove('hidden');
                                }}
                              />
                            ) : null}
                            <div className={`h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium ${employee.avatar ? 'hidden' : ''}`}>
                              {employee.initials || employee.name.charAt(0)}
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {employee.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              {employee.employeeCode}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {employee.designation}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {employee.department}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div>{employee.email}</div>
                        <div>{employee.phone}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEmployeeClick(employee);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          View Profile
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
      case 'analytics':
        return (
          <TeamAnalyticsView 
            analytics={analytics}
            employees={employees}
          />
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <LoadingWithTimeout
        isLoading={loading}
        timeout={30000}
        onRetry={loadTeamData}
        title="Loading Team Directory..."
        description="Fetching organizational data and employee information"
        showRetryAfter={10}
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
          <div className="text-red-500 mb-4">
            <Users className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Failed to Load Team Directory
          </h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Users className="w-8 h-8 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Team Directory</h1>
                  <p className="text-sm text-gray-500">
                    {employees.length} employees across {analytics?.departmentBreakdown.length || 0} departments
                  </p>
                  <p className="text-xs text-blue-600">
                    Debug: ViewMode={viewMode}, OrgChart={organizationChart.length} nodes
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                    showFilters || hasActiveFilters()
                      ? 'bg-blue-50 border-blue-200 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Filter className="w-4 h-4" />
                  <span>Filters</span>
                  {hasActiveFilters() && (
                    <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-1">
                      {Object.values(filters).flat().filter(Boolean).length}
                    </span>
                  )}
                </button>
                
                <ViewModeToggle 
                  viewMode={viewMode} 
                  onViewModeChange={setViewMode} 
                />
                
                <button
                  onClick={handleRefresh}
                  className="flex items-center space-x-2 px-3 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </button>
                
                <button
                  onClick={handleExport}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        {showFilters && (
          <SearchAndFilters
            filters={filters}
            onFiltersChange={setFilters}
            onClearFilters={clearFilters}
            employees={employees}
          />
        )}

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {renderContent()}
        </div>

        {/* Employee Profile Modal */}
        {selectedEmployee && (
          <EmployeeProfileModal
            employee={selectedEmployee}
            onClose={() => setSelectedEmployee(null)}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

export default TeamDirectoryMain;
