import React from 'react';
import { 
  Users, 
  TrendingUp, 
  Building, 
  Award, 
  MapPin, 
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  Target
} from 'lucide-react';
import { TeamAnalytics } from '../../types/teamDirectory';
import { TeamMember } from '../../types/team';

interface TeamAnalyticsViewProps {
  analytics: TeamAnalytics | null;
  employees: TeamMember[];
}

const TeamAnalyticsView: React.FC<TeamAnalyticsViewProps> = ({ analytics, employees }) => {
  if (!analytics) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <BarChart3 className="w-16 h-16 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Analytics...</h3>
        <p className="text-gray-500">Team analytics data is being processed.</p>
      </div>
    );
  }

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-2">{value}</p>
          {subtitle && (
            <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const DepartmentChart: React.FC = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Building className="w-5 h-5 text-gray-600" />
        <h3 className="text-lg font-semibold text-gray-900">Department Distribution</h3>
      </div>
      
      <div className="space-y-4">
        {analytics.departmentBreakdown.map((dept) => (
          <div key={dept.department} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: dept.color }}
              />
              <span className="font-medium text-gray-900">{dept.department}</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${dept.percentage}%`,
                    backgroundColor: dept.color 
                  }}
                />
              </div>
              <div className="text-right min-w-[4rem]">
                <div className="font-semibold text-gray-900">{dept.count}</div>
                <div className="text-sm text-gray-500">{dept.percentage}%</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const HierarchyChart: React.FC = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-2 mb-6">
        <Users className="w-5 h-5 text-gray-600" />
        <h3 className="text-lg font-semibold text-gray-900">Hierarchy Distribution</h3>
      </div>
      
      <div className="space-y-4">
        {analytics.hierarchyDistribution.map((level, index) => {
          const colors = ['bg-purple-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-gray-500'];
          const color = colors[index] || 'bg-gray-500';
          
          return (
            <div key={level.level} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 rounded-full ${color}`} />
                <span className="font-medium text-gray-900">{level.level}</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-32 bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${color}`}
                    style={{ width: `${level.percentage}%` }}
                  />
                </div>
                <div className="text-right min-w-[4rem]">
                  <div className="font-semibold text-gray-900">{level.count}</div>
                  <div className="text-sm text-gray-500">{level.percentage}%</div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  const QuickInsights: React.FC = () => {
    const leadershipCount = employees.filter(emp => emp.isLeadership).length;
    const teamMembersCount = employees.filter(emp => !emp.isLeadership).length;
    const leadershipRatio = leadershipCount > 0 ? Math.round(teamMembersCount / leadershipCount) : 0;
    
    const recentHires = employees.filter(emp => {
      if (!emp.joiningDate) return false;
      const joinDate = new Date(emp.joiningDate);
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
      return joinDate >= threeMonthsAgo;
    }).length;

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Target className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Quick Insights</h3>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
            <div>
              <p className="font-medium text-blue-900">Leadership Ratio</p>
              <p className="text-sm text-blue-700">Team members per leader</p>
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {leadershipRatio}:1
            </div>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
            <div>
              <p className="font-medium text-green-900">Recent Hires</p>
              <p className="text-sm text-green-700">Last 3 months</p>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {recentHires}
            </div>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
            <div>
              <p className="font-medium text-purple-900">Departments</p>
              <p className="text-sm text-purple-700">Active departments</p>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {analytics.departmentBreakdown.length}
            </div>
          </div>
          
          <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
            <div>
              <p className="font-medium text-orange-900">Avg Team Size</p>
              <p className="text-sm text-orange-700">Per manager</p>
            </div>
            <div className="text-2xl font-bold text-orange-600">
              {analytics.averageTeamSize}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Employees"
          value={analytics.totalEmployees}
          icon={<Users className="w-6 h-6 text-white" />}
          color="bg-blue-500"
          subtitle="Active employees"
        />
        
        <StatCard
          title="Departments"
          value={analytics.departmentBreakdown.length}
          icon={<Building className="w-6 h-6 text-white" />}
          color="bg-green-500"
          subtitle="Active departments"
        />
        
        <StatCard
          title="Leadership"
          value={employees.filter(emp => emp.isLeadership).length}
          icon={<Award className="w-6 h-6 text-white" />}
          color="bg-purple-500"
          subtitle="Managers & leads"
        />
        
        <StatCard
          title="Avg Team Size"
          value={analytics.averageTeamSize}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-orange-500"
          subtitle="Per manager"
        />
      </div>

      {/* Charts and Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <DepartmentChart />
          <HierarchyChart />
        </div>
        
        <div>
          <QuickInsights />
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <Calendar className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Tenure Analysis</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">&lt; 1 year</span>
              <span className="font-semibold">
                {employees.filter(emp => {
                  if (!emp.joiningDate) return false;
                  const joinDate = new Date(emp.joiningDate);
                  const oneYearAgo = new Date();
                  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                  return joinDate >= oneYearAgo;
                }).length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">1-3 years</span>
              <span className="font-semibold">
                {employees.filter(emp => {
                  if (!emp.joiningDate) return false;
                  const joinDate = new Date(emp.joiningDate);
                  const oneYearAgo = new Date();
                  const threeYearsAgo = new Date();
                  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                  threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
                  return joinDate < oneYearAgo && joinDate >= threeYearsAgo;
                }).length}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">3+ years</span>
              <span className="font-semibold">
                {employees.filter(emp => {
                  if (!emp.joiningDate) return false;
                  const joinDate = new Date(emp.joiningDate);
                  const threeYearsAgo = new Date();
                  threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
                  return joinDate < threeYearsAgo;
                }).length}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <PieChart className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Team Composition</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Leadership</span>
              <span className="font-semibold">
                {Math.round((employees.filter(emp => emp.isLeadership).length / analytics.totalEmployees) * 100)}%
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Team Members</span>
              <span className="font-semibold">
                {Math.round((employees.filter(emp => !emp.isLeadership).length / analytics.totalEmployees) * 100)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">Growth Metrics</h3>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">This Month</span>
              <span className="font-semibold text-green-600">+0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">This Quarter</span>
              <span className="font-semibold text-green-600">+0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">This Year</span>
              <span className="font-semibold text-green-600">+{analytics.totalEmployees}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamAnalyticsView;
