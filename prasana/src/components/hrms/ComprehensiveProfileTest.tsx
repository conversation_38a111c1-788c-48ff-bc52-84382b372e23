import React, { useState, useEffect } from 'react';
import { hrmsService } from '../../services/hrmsService';
import { 
  Employee, 
  EmployeeEducation, 
  EmployeeWorkExperience, 
  EmployeeFamilyMember,
  EmployeeCertification,
  EmployeeLanguage,
  EmployeeReference
} from '../../types/hrms';

const ComprehensiveProfileTest: React.FC = () => {
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testComprehensiveProfile = async () => {
    setLoading(true);
    setTestResults([]);
    
    try {
      addTestResult('Starting comprehensive profile test...');
      
      // Get current employee
      const currentEmployee = await hrmsService.getCurrentEmployee();
      if (!currentEmployee) {
        addTestResult('❌ No current employee found');
        return;
      }
      
      setEmployee(currentEmployee);
      addTestResult(`✅ Current employee loaded: ${currentEmployee.first_name} ${currentEmployee.last_name}`);
      
      // Test updating employee with comprehensive details
      const comprehensiveData = {
        blood_group: 'O+',
        religion: 'Test Religion',
        father_name: 'Test Father',
        mother_name: 'Test Mother',
        bank_name: 'Test Bank',
        pan_number: '**********',
        technical_skills: ['React', 'TypeScript', 'Node.js'],
        hobbies: ['Reading', 'Swimming'],
        medical_conditions: 'None'
      };
      
      await hrmsService.updateEmployee(currentEmployee.id, comprehensiveData);
      addTestResult('✅ Employee updated with comprehensive details');
      
      // Test adding education
      const education: Omit<EmployeeEducation, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        institution_name: 'Test University',
        degree: 'Bachelor of Technology',
        field_of_study: 'Computer Science',
        start_year: 2018,
        end_year: 2022,
        percentage_or_cgpa: '8.5',
        is_completed: true
      };
      
      const addedEducation = await hrmsService.addEmployeeEducation(education);
      addTestResult(`✅ Education added: ${addedEducation.degree} from ${addedEducation.institution_name}`);
      
      // Test adding work experience
      const workExp: Omit<EmployeeWorkExperience, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        company_name: 'Previous Company Ltd',
        designation: 'Software Developer',
        start_date: '2022-01-01',
        end_date: '2023-12-31',
        is_current: false,
        salary: 500000,
        location: 'Bangalore'
      };
      
      const addedWorkExp = await hrmsService.addEmployeeWorkExperience(workExp);
      addTestResult(`✅ Work experience added: ${addedWorkExp.designation} at ${addedWorkExp.company_name}`);
      
      // Test adding family member
      const familyMember: Omit<EmployeeFamilyMember, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        name: 'Test Family Member',
        relationship: 'Father',
        phone: '+91-9876543210',
        is_dependent: false,
        is_emergency_contact: true
      };
      
      const addedFamilyMember = await hrmsService.addEmployeeFamilyMember(familyMember);
      addTestResult(`✅ Family member added: ${addedFamilyMember.name} (${addedFamilyMember.relationship})`);
      
      // Test adding certification
      const certification: Omit<EmployeeCertification, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        certification_name: 'AWS Certified Developer',
        issuing_organization: 'Amazon Web Services',
        issue_date: '2023-06-01',
        expiry_date: '2026-06-01'
      };
      
      const addedCertification = await hrmsService.addEmployeeCertification(certification);
      addTestResult(`✅ Certification added: ${addedCertification.certification_name}`);
      
      // Test adding language
      const language: Omit<EmployeeLanguage, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        language: 'English',
        proficiency: 'Advanced',
        can_read: true,
        can_write: true,
        can_speak: true
      };
      
      const addedLanguage = await hrmsService.addEmployeeLanguage(language);
      addTestResult(`✅ Language added: ${addedLanguage.language} (${addedLanguage.proficiency})`);
      
      // Test adding reference
      const reference: Omit<EmployeeReference, 'id' | 'created_at'> = {
        employee_id: currentEmployee.id,
        name: 'John Doe',
        designation: 'Senior Manager',
        company: 'Previous Company Ltd',
        relationship: 'Ex-Manager',
        email: '<EMAIL>',
        phone: '+91-9876543210',
        years_known: 2
      };
      
      const addedReference = await hrmsService.addEmployeeReference(reference);
      addTestResult(`✅ Reference added: ${addedReference.name} (${addedReference.relationship})`);
      
      // Test loading comprehensive details
      const comprehensiveEmployee = await hrmsService.getEmployeeWithComprehensiveDetails(currentEmployee.id);
      if (comprehensiveEmployee) {
        addTestResult(`✅ Comprehensive employee data loaded with ${comprehensiveEmployee.education?.length || 0} education records`);
        addTestResult(`✅ Work experience: ${comprehensiveEmployee.work_experience?.length || 0} records`);
        addTestResult(`✅ Family members: ${comprehensiveEmployee.family_members?.length || 0} records`);
        addTestResult(`✅ Certifications: ${comprehensiveEmployee.employee_certifications?.length || 0} records`);
        addTestResult(`✅ Languages: ${comprehensiveEmployee.employee_languages?.length || 0} records`);
        addTestResult(`✅ References: ${comprehensiveEmployee.references?.length || 0} records`);
      }
      
      addTestResult('🎉 All comprehensive profile tests completed successfully!');
      
    } catch (error) {
      addTestResult(`❌ Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Test error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-bold mb-4">Comprehensive Profile Test</h2>
        
        {employee && (
          <div className="mb-4 p-4 bg-blue-50 rounded-lg">
            <p><strong>Testing with employee:</strong> {employee.first_name} {employee.last_name}</p>
            <p><strong>Employee ID:</strong> {employee.employee_id}</p>
            <p><strong>Email:</strong> {employee.email}</p>
          </div>
        )}
        
        <button
          onClick={testComprehensiveProfile}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg disabled:opacity-50 mb-4"
        >
          {loading ? 'Running Tests...' : 'Run Comprehensive Profile Tests'}
        </button>
        
        <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
          <h3 className="font-semibold mb-2">Test Results:</h3>
          {testResults.length === 0 ? (
            <p className="text-gray-500">No tests run yet. Click the button above to start testing.</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-800 mb-2">Note:</h3>
          <p className="text-yellow-700 text-sm">
            This test component verifies that all comprehensive personal detail features are working correctly.
            It tests adding education, work experience, family members, certifications, languages, and references.
            Make sure the database migration has been applied before running these tests.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveProfileTest;
