import React, { useState } from 'react';
import {
  LayoutDashboard,
  User,
  Clock,
  Calendar,
  FileText,
  DollarSign,
  TrendingUp,
  Settings,
  Users,
  Bell,
  Shield
} from 'lucide-react';
import { HRMSProvider, useHRMS } from '../../contexts/HRMSContext';
import ErrorBoundary from '../ErrorBoundary';
import LoadingWithTimeout from '../LoadingWithTimeout';
import { useRetryOnVisible } from '../../hooks/useTabVisibility';
import HRMSDashboard from './HRMSDashboard';
import EmployeeProfile from './EmployeeProfile';

import LeaveManagement from './LeaveManagement';
import HRMSTimesheet from './HRMSTimesheet';
import PerformanceManagement from './PerformanceManagement';
import DocumentManagement from './DocumentManagement';
import SimpleRoleManagement from './SimpleRoleManagement';
import HRMSDebug from './HRMSDebug';

import PayrollManagement from '../payroll/PayrollManagement';

type HRMSPage =
  | 'dashboard'
  | 'profile'
  | 'leave'
  | 'timesheet'
  | 'payroll'
  | 'performance'
  | 'documents'
  | 'roles'

  | 'settings';

interface NavigationItem {
  id: HRMSPage;
  name: string;
  icon: React.ReactNode;
  description: string;
  comingSoon?: boolean;
}

const HRMSContent: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<HRMSPage>('dashboard');
  const { currentEmployee, unreadNotificationCount, totalAvailableLeave, loading, error, retryInitialization } = useHRMS();

  // Auto-retry when tab becomes visible if there's an error
  useRetryOnVisible(retryInitialization, !!error, 2000);

  // Show loading state with timeout protection
  if (loading) {
    return (
      <LoadingWithTimeout
        isLoading={loading}
        timeout={30000} // 30 seconds
        onRetry={retryInitialization}
        title="Loading HRMS..."
        description="Setting up your employee profile"
        showRetryAfter={10} // Show retry after 10 seconds
      />
    );
  }

  // Show error state if there's an error or no employee after loading
  if (!loading && (error || !currentEmployee)) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-100">
        <div className="text-center max-w-md">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Unable to Load HRMS</h2>
          <p className="text-gray-500 mb-4">
            {error || 'There was an issue setting up your employee profile. Please try again.'}
          </p>
          <div className="space-x-2">
            <button
              onClick={retryInitialization}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Retry
            </button>
            <button
              onClick={() => window.location.reload()}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Refresh Page
            </button>
            <button
              onClick={() => setCurrentPage('debug' as any)}
              className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Debug Info
            </button>
          </div>

          {/* Additional help text */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Tip:</strong> If this keeps happening, try switching to another tab and back,
              or check your internet connection.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: <LayoutDashboard className="w-5 h-5" />,
      description: 'Overview and quick actions'
    },
    {
      id: 'profile',
      name: 'My Profile',
      icon: <User className="w-5 h-5" />,
      description: 'Personal information and documents'
    },
    {
      id: 'leave',
      name: 'Leave Management',
      icon: <Calendar className="w-5 h-5" />,
      description: 'Apply and track leave requests',
      comingSoon: true
    },
    {
      id: 'timesheet',
      name: 'Timesheet',
      icon: <Clock className="w-5 h-5" />,
      description: 'Manual time entry and tracking'
    },
    {
      id: 'payroll',
      name: 'Payroll',
      icon: <DollarSign className="w-5 h-5" />,
      description: 'Salary, payslips and compensation',
      comingSoon: true
    },
    {
      id: 'performance',
      name: 'Performance',
      icon: <TrendingUp className="w-5 h-5" />,
      description: 'Goals and performance reviews',
      comingSoon: true
    },
    {
      id: 'documents',
      name: 'Documents',
      icon: <FileText className="w-5 h-5" />,
      description: 'Personal documents and certificates'
    },
    {
      id: 'roles',
      name: 'Role Management',
      icon: <Shield className="w-5 h-5" />,
      description: 'Manage roles and permissions',
      comingSoon: true
    },

  ];

  const renderContent = () => {
    switch (currentPage) {
      case 'dashboard':
        return <HRMSDashboard onNavigate={setCurrentPage} />;
      case 'profile':
        return <EmployeeProfile />;
      case 'leave':
        return <ComingSoonPage title="Leave Management" />;
      case 'timesheet':
        return <HRMSTimesheet />;
      case 'payroll':
        return <ComingSoonPage title="Payroll Management" />;
      case 'performance':
        return <ComingSoonPage title="Performance Management" />;
      case 'documents':
        return <DocumentManagement />;
      case 'roles':
        return <ComingSoonPage title="Role Management" />;

      case 'settings':
        return <ComingSoonPage title="Settings" />;
      case 'debug' as any:
        return <HRMSDebug />;
      default:
        return <HRMSDashboard onNavigate={setCurrentPage} />;
    }
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* HRMS Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">HRMS</h2>
          <p className="text-sm text-gray-600">Human Resource Management</p>
        </div>
        
        <nav className="mt-6">
          <div className="px-3">
            {navigationItems.map((item) => (
              <button
                key={item.id}
                onClick={() => !item.comingSoon && setCurrentPage(item.id)}
                className={`w-full flex items-center px-3 py-3 mb-1 text-left rounded-lg transition-colors ${
                  currentPage === item.id
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : item.comingSoon
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }`}
                disabled={item.comingSoon}
              >
                <span className="mr-3">{item.icon}</span>
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className="font-medium">{item.name}</span>
                    {item.comingSoon && (
                      <span className="ml-2 px-2 py-0.5 text-xs bg-gray-200 text-gray-600 rounded-full">
                        Soon
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-0.5">{item.description}</p>
                </div>
              </button>
            ))}
          </div>
        </nav>

        {/* Quick Stats */}
        <div className="mt-8 px-6">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-4 text-white">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-100 text-sm">Leave Balance</p>
                <p className="text-xl font-bold">{totalAvailableLeave}</p>
                <p className="text-blue-100 text-xs">Days available</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-200" />
            </div>
          </div>
        </div>

        {/* Notifications */}
        <div className="mt-6 px-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
            <div className="relative">
              <Bell className="w-4 h-4 text-gray-400" />
              {unreadNotificationCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {unreadNotificationCount}
                </span>
              )}
            </div>
          </div>
          <div className="space-y-2">
            {unreadNotificationCount > 0 ? (
              <>
                <div className="p-2 bg-yellow-50 rounded-lg border-l-2 border-yellow-400">
                  <p className="text-xs text-yellow-800">You have {unreadNotificationCount} unread notifications</p>
                </div>
                <button
                  onClick={() => setCurrentPage('dashboard')}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  View all notifications
                </button>
              </>
            ) : (
              <div className="p-2 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600">No new notifications</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

// Coming Soon Component
const ComingSoonPage: React.FC<{ title: string }> = ({ title }) => {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="w-24 h-24 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center">
          <Settings className="w-12 h-12 text-blue-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
        <p className="text-gray-600 mb-6">This feature is coming soon!</p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
          <p className="text-sm text-blue-800">
            We're working hard to bring you this feature. Stay tuned for updates!
          </p>
        </div>
      </div>
    </div>
  );
};

// Main HRMS Component with Provider and Error Boundary
const HRMSMain: React.FC = () => {
  return (
    <ErrorBoundary>
      <HRMSProvider>
        <HRMSContent />
      </HRMSProvider>
    </ErrorBoundary>
  );
};

export default HRMSMain;
