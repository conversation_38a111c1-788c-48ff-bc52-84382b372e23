import React, { useState, useEffect } from 'react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Edit3,
  Save,
  X,
  Upload,
  FileText,
  Download,
  GraduationCap,
  Building,
  Users,
  Award,
  Languages,
  UserCheck,
  Heart,
  CreditCard,
  Plus,
  Trash2,
  Home,
  Globe
} from 'lucide-react';
import { hrmsService } from '../../services/hrmsService';
import ProfilePicture from '../ProfilePicture';
import {
  Employee,
  EmployeeDocument,
  EmployeeEducation,
  EmployeeWorkExperience,
  EmployeeFamilyMember,
  EmployeeCertification,
  EmployeeLanguage,
  EmployeeReference,
  EmployeeFormData,
  EMPLOYMENT_TYPES,
  GENDER_OPTIONS,
  MARITAL_STATUS_OPTIONS,
  BLOOD_GROUP_OPTIONS,
  CATEGORY_OPTIONS,
  RELATIONSHIP_OPTIONS,
  LANGUAGE_PROFICIENCY_OPTIONS,
  REFERENCE_RELATIONSHIP_OPTIONS
} from '../../types/hrms';

interface EmployeeProfileProps {
  employeeId?: string; // If not provided, shows current user's profile
  isEditable?: boolean;
}

const EmployeeProfile: React.FC<EmployeeProfileProps> = ({ 
  employeeId, 
  isEditable = true 
}) => {
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [documents, setDocuments] = useState<EmployeeDocument[]>([]);
  const [education, setEducation] = useState<EmployeeEducation[]>([]);
  const [workExperience, setWorkExperience] = useState<EmployeeWorkExperience[]>([]);
  const [familyMembers, setFamilyMembers] = useState<EmployeeFamilyMember[]>([]);
  const [certifications, setCertifications] = useState<EmployeeCertification[]>([]);
  const [languages, setLanguages] = useState<EmployeeLanguage[]>([]);
  const [references, setReferences] = useState<EmployeeReference[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<EmployeeFormData>({
    first_name: '',
    last_name: '',
    email: '',
    employee_id: '',
    joining_date: ''
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [uploadingDocument, setUploadingDocument] = useState(false);

  useEffect(() => {
    loadEmployeeData();
  }, [employeeId]);

  const loadEmployeeData = async () => {
    try {
      setLoading(true);
      
      let employeeData: Employee | null;
      if (employeeId) {
        employeeData = await hrmsService.getEmployeeById(employeeId);
      } else {
        employeeData = await hrmsService.getCurrentEmployee();
      }
      
      setEmployee(employeeData);
      
      if (employeeData) {
        setFormData({
          first_name: employeeData.first_name,
          last_name: employeeData.last_name,
          email: employeeData.email,
          phone: employeeData.phone || '',
          date_of_birth: employeeData.date_of_birth || '',
          gender: employeeData.gender || '',
          marital_status: employeeData.marital_status || '',
          nationality: employeeData.nationality || '',
          employee_id: employeeData.employee_id,
          designation: employeeData.designation || '',
          department: employeeData.department || '',
          team: employeeData.team || '',
          location: employeeData.location || '',
          employment_type: employeeData.employment_type || '',
          joining_date: employeeData.joining_date,
          personal_email: employeeData.personal_email || '',
          emergency_contact_name: employeeData.emergency_contact_name || '',
          emergency_contact_phone: employeeData.emergency_contact_phone || '',
          emergency_contact_relationship: employeeData.emergency_contact_relationship || '',
          address_line1: employeeData.address_line1 || '',
          address_line2: employeeData.address_line2 || '',
          city: employeeData.city || '',
          state: employeeData.state || '',
          postal_code: employeeData.postal_code || '',
          country: employeeData.country || '',

          // Additional Personal Details
          permanent_address_line1: employeeData.permanent_address_line1 || '',
          permanent_address_line2: employeeData.permanent_address_line2 || '',
          permanent_city: employeeData.permanent_city || '',
          permanent_state: employeeData.permanent_state || '',
          permanent_postal_code: employeeData.permanent_postal_code || '',
          permanent_country: employeeData.permanent_country || '',
          blood_group: employeeData.blood_group || '',
          religion: employeeData.religion || '',
          caste: employeeData.caste || '',
          category: employeeData.category || '',
          physically_challenged: employeeData.physically_challenged || false,
          identification_marks: employeeData.identification_marks || '',

          // Family Information
          father_name: employeeData.father_name || '',
          mother_name: employeeData.mother_name || '',
          spouse_name: employeeData.spouse_name || '',
          spouse_occupation: employeeData.spouse_occupation || '',
          spouse_phone: employeeData.spouse_phone || '',
          children_count: employeeData.children_count || 0,

          // Additional Contact Information
          alternate_phone: employeeData.alternate_phone || '',
          alternate_email: employeeData.alternate_email || '',
          linkedin_profile: employeeData.linkedin_profile || '',

          // Bank Details
          bank_name: employeeData.bank_name || '',
          bank_account_number: employeeData.bank_account_number || '',
          bank_ifsc_code: employeeData.bank_ifsc_code || '',
          bank_branch: employeeData.bank_branch || '',
          pan_number: employeeData.pan_number || '',
          aadhar_number: employeeData.aadhar_number || '',
          passport_number: employeeData.passport_number || '',
          passport_expiry_date: employeeData.passport_expiry_date || '',
          driving_license_number: employeeData.driving_license_number || '',
          driving_license_expiry_date: employeeData.driving_license_expiry_date || '',

          // Professional Details
          years_of_experience: employeeData.years_of_experience || 0,
          previous_company: employeeData.previous_company || '',
          previous_designation: employeeData.previous_designation || '',
          previous_salary: employeeData.previous_salary || 0,
          notice_period_days: employeeData.notice_period_days || 0,

          // Skills and Languages
          technical_skills: employeeData.technical_skills || [],
          soft_skills: employeeData.soft_skills || [],
          languages_known: employeeData.languages_known || [],
          certifications: employeeData.certifications || [],

          // Personal Interests
          hobbies: employeeData.hobbies || [],
          interests: employeeData.interests || [],
          achievements: employeeData.achievements || [],

          // Health Information
          medical_conditions: employeeData.medical_conditions || '',
          allergies: employeeData.allergies || ''
        });

        // Load documents and comprehensive details
        const [docs, edu, workExp, family, certs, langs, refs] = await Promise.all([
          hrmsService.getEmployeeDocuments(employeeData.id),
          hrmsService.getEmployeeEducation(employeeData.id),
          hrmsService.getEmployeeWorkExperience(employeeData.id),
          hrmsService.getEmployeeFamilyMembers(employeeData.id),
          hrmsService.getEmployeeCertifications(employeeData.id),
          hrmsService.getEmployeeLanguages(employeeData.id),
          hrmsService.getEmployeeReferences(employeeData.id)
        ]);

        setDocuments(docs);
        setEducation(edu);
        setWorkExperience(workExp);
        setFamilyMembers(family);
        setCertifications(certs);
        setLanguages(langs);
        setReferences(refs);
      }
    } catch (error) {
      console.error('Error loading employee data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!employee) return;
    
    try {
      setSaving(true);
      await hrmsService.updateEmployee(employee.id, formData);
      setIsEditing(false);
      await loadEmployeeData(); // Refresh data
    } catch (error) {
      console.error('Error saving employee data:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data
    if (employee) {
      setFormData({
        first_name: employee.first_name,
        last_name: employee.last_name,
        email: employee.email,
        phone: employee.phone || '',
        date_of_birth: employee.date_of_birth || '',
        gender: employee.gender || '',
        marital_status: employee.marital_status || '',
        nationality: employee.nationality || '',
        employee_id: employee.employee_id,
        designation: employee.designation || '',
        department: employee.department || '',
        team: employee.team || '',
        location: employee.location || '',
        employment_type: employee.employment_type || '',
        joining_date: employee.joining_date,
        personal_email: employee.personal_email || '',
        emergency_contact_name: employee.emergency_contact_name || '',
        emergency_contact_phone: employee.emergency_contact_phone || '',
        emergency_contact_relationship: employee.emergency_contact_relationship || '',
        address_line1: employee.address_line1 || '',
        address_line2: employee.address_line2 || '',
        city: employee.city || '',
        state: employee.state || '',
        postal_code: employee.postal_code || '',
        country: employee.country || ''
      });
    }
  };

  const handleDocumentUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !employee) return;

    try {
      setUploadingDocument(true);
      const documentType = 'OTHER'; // You can add a dropdown to select document type
      await hrmsService.uploadDocument(file, employee.id, documentType);
      await loadEmployeeData(); // Refresh documents
    } catch (error) {
      console.error('Error uploading document:', error);
    } finally {
      setUploadingDocument(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!employee) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Employee not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <ProfilePicture
              userId={employee.user_id || employee.id}
              name={employee.full_name || `${employee.first_name} ${employee.last_name}`}
              email={employee.email}
              profilePictureUrl={employee.profile_picture_url}
              size="lg"
              className="border-2 border-blue-200 shadow-lg"
            />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {employee.first_name} {employee.last_name}
              </h1>
              <p className="text-gray-600">{employee.designation} • {employee.department}</p>
              <p className="text-sm text-gray-500">Employee ID: {employee.employee_id}</p>
            </div>
          </div>
          {isEditable && (
            <div className="flex space-x-2">
              {isEditing ? (
                <>
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    <span>{saving ? 'Saving...' : 'Save'}</span>
                  </button>
                  <button
                    onClick={handleCancel}
                    className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                  >
                    <X className="w-4 h-4" />
                    <span>Cancel</span>
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
                >
                  <Edit3 className="w-4 h-4" />
                  <span>Edit Profile</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Personal Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Personal Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">First Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.first_name}
                onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.first_name}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.last_name}
                onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.last_name}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            {isEditing ? (
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.email}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.phone || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
            {isEditing ? (
              <input
                type="date"
                value={formData.date_of_birth}
                onChange={(e) => setFormData({ ...formData, date_of_birth: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">
                {employee.date_of_birth ? new Date(employee.date_of_birth).toLocaleDateString() : 'Not provided'}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Gender</label>
            {isEditing ? (
              <select
                value={formData.gender}
                onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Gender</option>
                {GENDER_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.gender || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Marital Status</label>
            {isEditing ? (
              <select
                value={formData.marital_status}
                onChange={(e) => setFormData({ ...formData, marital_status: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Status</option>
                {MARITAL_STATUS_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.marital_status || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Nationality</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.nationality}
                onChange={(e) => setFormData({ ...formData, nationality: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.nationality || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Blood Group</label>
            {isEditing ? (
              <select
                value={formData.blood_group}
                onChange={(e) => setFormData({ ...formData, blood_group: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Blood Group</option>
                {BLOOD_GROUP_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.blood_group || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Additional Personal Details */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <Heart className="w-5 h-5 mr-2" />
          Additional Personal Details
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Religion</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.religion}
                onChange={(e) => setFormData({ ...formData, religion: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.religion || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
            {isEditing ? (
              <select
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Category</option>
                {CATEGORY_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.category || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Father's Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.father_name}
                onChange={(e) => setFormData({ ...formData, father_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.father_name || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Mother's Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.mother_name}
                onChange={(e) => setFormData({ ...formData, mother_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.mother_name || 'Not provided'}</p>
            )}
          </div>
          {employee.marital_status === 'Married' && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Spouse Name</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.spouse_name}
                    onChange={(e) => setFormData({ ...formData, spouse_name: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.spouse_name || 'Not provided'}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Spouse Occupation</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.spouse_occupation}
                    onChange={(e) => setFormData({ ...formData, spouse_occupation: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.spouse_occupation || 'Not provided'}</p>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Address Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <Home className="w-5 h-5 mr-2" />
          Address Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Address */}
          <div>
            <h3 className="text-md font-medium mb-3 text-gray-800">Current Address</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.address_line1}
                    onChange={(e) => setFormData({ ...formData, address_line1: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.address_line1 || 'Not provided'}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.address_line2}
                    onChange={(e) => setFormData({ ...formData, address_line2: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.address_line2 || 'Not provided'}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.city || 'Not provided'}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.state}
                      onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.state || 'Not provided'}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.postal_code}
                      onChange={(e) => setFormData({ ...formData, postal_code: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.postal_code || 'Not provided'}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.country}
                      onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.country || 'Not provided'}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Permanent Address */}
          <div>
            <h3 className="text-md font-medium mb-3 text-gray-800">Permanent Address</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 1</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.permanent_address_line1}
                    onChange={(e) => setFormData({ ...formData, permanent_address_line1: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_address_line1 || 'Not provided'}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Address Line 2</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={formData.permanent_address_line2}
                    onChange={(e) => setFormData({ ...formData, permanent_address_line2: e.target.value })}
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_address_line2 || 'Not provided'}</p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.permanent_city}
                      onChange={(e) => setFormData({ ...formData, permanent_city: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_city || 'Not provided'}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.permanent_state}
                      onChange={(e) => setFormData({ ...formData, permanent_state: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_state || 'Not provided'}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.permanent_postal_code}
                      onChange={(e) => setFormData({ ...formData, permanent_postal_code: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_postal_code || 'Not provided'}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                  {isEditing ? (
                    <input
                      type="text"
                      value={formData.permanent_country}
                      onChange={(e) => setFormData({ ...formData, permanent_country: e.target.value })}
                      className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  ) : (
                    <p className="p-2 bg-gray-50 rounded-lg">{employee.permanent_country || 'Not provided'}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Job Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <Briefcase className="w-5 h-5 mr-2" />
          Job Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Designation</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.designation}
                onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.designation || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.department}
                onChange={(e) => setFormData({ ...formData, department: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.department || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Team</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.team}
                onChange={(e) => setFormData({ ...formData, team: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.team || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Employment Type</label>
            {isEditing ? (
              <select
                value={formData.employment_type}
                onChange={(e) => setFormData({ ...formData, employment_type: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">Select Type</option>
                {EMPLOYMENT_TYPES.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.employment_type || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Bank & Identity Details */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <CreditCard className="w-5 h-5 mr-2" />
          Bank & Identity Details
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.bank_name}
                onChange={(e) => setFormData({ ...formData, bank_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.bank_name || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.bank_account_number}
                onChange={(e) => setFormData({ ...formData, bank_account_number: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.bank_account_number ? '****' + employee.bank_account_number.slice(-4) : 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">IFSC Code</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.bank_ifsc_code}
                onChange={(e) => setFormData({ ...formData, bank_ifsc_code: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.bank_ifsc_code || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Bank Branch</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.bank_branch}
                onChange={(e) => setFormData({ ...formData, bank_branch: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.bank_branch || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">PAN Number</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.pan_number}
                onChange={(e) => setFormData({ ...formData, pan_number: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.pan_number || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Aadhar Number</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.aadhar_number}
                onChange={(e) => setFormData({ ...formData, aadhar_number: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.aadhar_number ? '****-****-' + employee.aadhar_number.slice(-4) : 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Passport Number</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.passport_number}
                onChange={(e) => setFormData({ ...formData, passport_number: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.passport_number || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Driving License</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.driving_license_number}
                onChange={(e) => setFormData({ ...formData, driving_license_number: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.driving_license_number || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4 flex items-center">
          <Phone className="w-5 h-5 mr-2" />
          Contact Information
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Personal Email</label>
            {isEditing ? (
              <input
                type="email"
                value={formData.personal_email}
                onChange={(e) => setFormData({ ...formData, personal_email: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.personal_email || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Alternate Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={formData.alternate_phone}
                onChange={(e) => setFormData({ ...formData, alternate_phone: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.alternate_phone || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn Profile</label>
            {isEditing ? (
              <input
                type="url"
                value={formData.linkedin_profile}
                onChange={(e) => setFormData({ ...formData, linkedin_profile: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">
                {employee.linkedin_profile ? (
                  <a href={employee.linkedin_profile} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
                    View Profile
                  </a>
                ) : (
                  'Not provided'
                )}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.emergency_contact_name}
                onChange={(e) => setFormData({ ...formData, emergency_contact_name: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.emergency_contact_name || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Phone</label>
            {isEditing ? (
              <input
                type="tel"
                value={formData.emergency_contact_phone}
                onChange={(e) => setFormData({ ...formData, emergency_contact_phone: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.emergency_contact_phone || 'Not provided'}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Emergency Contact Relationship</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.emergency_contact_relationship}
                onChange={(e) => setFormData({ ...formData, emergency_contact_relationship: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            ) : (
              <p className="p-2 bg-gray-50 rounded-lg">{employee.emergency_contact_relationship || 'Not provided'}</p>
            )}
          </div>
        </div>
      </div>

      {/* Documents */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Documents
          </h2>
          {isEditable && (
            <label className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 cursor-pointer">
              <Upload className="w-4 h-4" />
              <span>{uploadingDocument ? 'Uploading...' : 'Upload Document'}</span>
              <input
                type="file"
                onChange={handleDocumentUpload}
                className="hidden"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                disabled={uploadingDocument}
              />
            </label>
          )}
        </div>
        <div className="space-y-3">
          {documents.map((doc) => (
            <div key={doc.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium">{doc.document_name}</p>
                  <p className="text-sm text-gray-500">{doc.document_type}</p>
                </div>
              </div>
              <a
                href={doc.file_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 hover:text-blue-600 flex items-center space-x-1"
              >
                <Download className="w-4 h-4" />
                <span>Download</span>
              </a>
            </div>
          ))}
          {documents.length === 0 && (
            <p className="text-gray-500 text-center py-4">No documents uploaded</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeProfile;
