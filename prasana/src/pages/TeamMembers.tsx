import React from 'react'

const TeamMembers: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            HR Dashboard - Team Members
          </h1>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-2">
              Welcome to Technosprint HR Dashboard
            </h2>
            <p className="text-blue-700">
              This is your team management interface. The application is now running successfully!
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">Total Employees</h3>
              <p className="text-3xl font-bold">29</p>
              <p className="text-blue-100 text-sm">Active members</p>
            </div>
            
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">Teams</h3>
              <p className="text-3xl font-bold">4</p>
              <p className="text-green-100 text-sm">Active teams</p>
            </div>
            
            <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">Projects</h3>
              <p className="text-3xl font-bold">12</p>
              <p className="text-purple-100 text-sm">Ongoing projects</p>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="flex flex-wrap gap-4">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                Add Employee
              </button>
              <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors">
                View Reports
              </button>
              <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors">
                Manage Teams
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TeamMembers
