-- =====================================================
-- UPDATE EMPLOYEE IDs TO PS NUMBERS (TIS CODES)
-- Replaces EMP format with TIS format for all employees
-- =====================================================

-- First, let's add a temporary column to store the new PS numbers
ALTER TABLE employees ADD COLUMN IF NOT EXISTS ps_number VARCHAR(20);

-- Update employees with their correct PS numbers based on names
-- Leadership Team
UPDATE employees SET ps_number = 'TIS00003' WHERE first_name ILIKE '%arun%' AND last_name ILIKE '%g%';
UPDATE employees SET ps_number = 'TIS00012' WHERE first_name ILIKE '%eashwar%' AND (last_name ILIKE '%prasadh%' OR last_name ILIKE '%prasad%');
UPDATE employees SET ps_number = 'TIS00011' WHERE first_name ILIKE '%yuvaraj%' AND last_name ILIKE '%s%';
UPDATE employees SET ps_number = 'TIS00013' WHERE first_name ILIKE '%sri%' AND first_name ILIKE '%ram%';
UPDATE employees SET ps_number = 'TIS00018' WHERE first_name ILIKE '%aamina%' AND last_name ILIKE '%begam%';

-- Technical and Client Managers
UPDATE employees SET ps_number = 'TIS00020' WHERE first_name ILIKE '%selvendrane%' OR first_name ILIKE '%selvandrane%';
UPDATE employees SET ps_number = 'TIS00030' WHERE first_name ILIKE '%purushoth%';
UPDATE employees SET ps_number = 'TIS00019' WHERE first_name ILIKE '%yusuf%' AND last_name ILIKE '%fayas%';
UPDATE employees SET ps_number = 'TIS00024' WHERE first_name ILIKE '%gowtham%' OR (first_name ILIKE '%kollati%' AND last_name ILIKE '%gowtham%');
UPDATE employees SET ps_number = 'TIS00017' WHERE first_name ILIKE '%maheshwaran%';
UPDATE employees SET ps_number = 'TIS00025' WHERE first_name ILIKE '%kiyshore%' OR first_name ILIKE '%kiyshor%';
UPDATE employees SET ps_number = 'TIS00010' WHERE first_name ILIKE '%darshan%' AND last_name ILIKE '%k%';
UPDATE employees SET ps_number = 'TIS00016' WHERE first_name ILIKE '%prasanna%' AND last_name ILIKE '%venkatesh%';

-- Team Members
UPDATE employees SET ps_number = 'TIS00031' WHERE first_name ILIKE '%fazeela%';
UPDATE employees SET ps_number = 'TIS00033' WHERE first_name ILIKE '%sivarajani%' OR first_name ILIKE '%sivaranjani%';
UPDATE employees SET ps_number = 'TIS00023' WHERE first_name ILIKE '%praveen%' AND (last_name ILIKE '%dommeti%' OR first_name ILIKE '%dommeti%');
UPDATE employees SET ps_number = 'TIS00034' WHERE first_name ILIKE '%nitesh%' AND last_name ILIKE '%s%';
UPDATE employees SET ps_number = 'TIS00038' WHERE first_name ILIKE '%nithish%' AND last_name ILIKE '%k%';
UPDATE employees SET ps_number = 'TIS00036' WHERE first_name ILIKE '%sakthivel%' AND last_name ILIKE '%n%';
UPDATE employees SET ps_number = 'TIS00040' WHERE first_name ILIKE '%gaushik%';
UPDATE employees SET ps_number = 'TIS00037' WHERE first_name ILIKE '%hariharan%' AND last_name ILIKE '%b%';
UPDATE employees SET ps_number = 'TIS00035' WHERE first_name ILIKE '%yamini%' AND last_name ILIKE '%selvam%';
UPDATE employees SET ps_number = 'TIS00032' WHERE first_name ILIKE '%keerthipriya%' OR (first_name ILIKE '%s%' AND last_name ILIKE '%keerthipriya%');
UPDATE employees SET ps_number = 'TIS00039' WHERE first_name ILIKE '%shri%' AND last_name ILIKE '%mathi%';

-- Left Members (set as inactive)
UPDATE employees SET ps_number = 'TIS00014', status = 'inactive' WHERE first_name ILIKE '%afra%' AND last_name ILIKE '%m%';
UPDATE employees SET ps_number = 'TIS00015', status = 'inactive' WHERE first_name ILIKE '%najla%';
UPDATE employees SET ps_number = 'TIS00021', status = 'inactive' WHERE first_name ILIKE '%rakesh%' AND last_name ILIKE '%s%';
UPDATE employees SET ps_number = 'TIS00026', status = 'inactive' WHERE first_name ILIKE '%deepak%' AND last_name ILIKE '%s%';
UPDATE employees SET ps_number = 'TIS00028', status = 'inactive' WHERE first_name ILIKE '%kanimozhi%';
UPDATE employees SET ps_number = 'TIS00027', status = 'inactive' WHERE first_name ILIKE '%rizwan%' OR first_name ILIKE '%mohamed%';
UPDATE employees SET ps_number = 'TIS00022', status = 'inactive' WHERE first_name ILIKE '%theepatharan%';

-- Now update the employee_id column with the PS numbers
UPDATE employees SET employee_id = ps_number WHERE ps_number IS NOT NULL;

-- Update any employees that don't have PS numbers yet (fallback)
UPDATE employees SET employee_id = 'TIS' || LPAD((ROW_NUMBER() OVER (ORDER BY created_at))::text, 5, '0') 
WHERE ps_number IS NULL AND employee_id LIKE 'EMP%';

-- Drop the temporary ps_number column
ALTER TABLE employees DROP COLUMN IF EXISTS ps_number;

-- Update any foreign key references in other tables
-- Update leave_applications table
UPDATE leave_applications 
SET employee_id = e.employee_id 
FROM employees e 
WHERE leave_applications.employee_id = e.id;

-- Update attendance table if it exists
UPDATE attendance 
SET employee_id = e.employee_id 
FROM employees e 
WHERE attendance.employee_id = e.id;

-- Update any other tables that reference employee_id
-- Add more UPDATE statements here for other tables as needed

-- Create index on employee_id for better performance
CREATE INDEX IF NOT EXISTS idx_employees_employee_id ON employees(employee_id);

-- Add constraint to ensure employee_id follows TIS format
ALTER TABLE employees ADD CONSTRAINT chk_employee_id_format 
CHECK (employee_id ~ '^TIS[0-9]{5}$');

-- Update the employee_id sequence if it exists
-- This ensures new employees get TIS format IDs
DO $$
BEGIN
    -- Find the highest TIS number and set sequence accordingly
    PERFORM setval(
        'employees_employee_id_seq', 
        COALESCE(
            (SELECT MAX(CAST(SUBSTRING(employee_id FROM 4) AS INTEGER)) 
             FROM employees 
             WHERE employee_id ~ '^TIS[0-9]{5}$'), 
            40
        ) + 1,
        false
    );
EXCEPTION
    WHEN undefined_table THEN
        -- Sequence doesn't exist, create it
        CREATE SEQUENCE IF NOT EXISTS employees_employee_id_seq START WITH 41;
END $$;

-- Create a function to generate new TIS employee IDs
CREATE OR REPLACE FUNCTION generate_employee_id()
RETURNS TEXT AS $$
DECLARE
    next_num INTEGER;
    new_id TEXT;
BEGIN
    -- Get the next number from sequence
    SELECT nextval('employees_employee_id_seq') INTO next_num;
    
    -- Format as TIS00XXX
    new_id := 'TIS' || LPAD(next_num::text, 5, '0');
    
    -- Check if ID already exists, if so, increment and try again
    WHILE EXISTS (SELECT 1 FROM employees WHERE employee_id = new_id) LOOP
        SELECT nextval('employees_employee_id_seq') INTO next_num;
        new_id := 'TIS' || LPAD(next_num::text, 5, '0');
    END LOOP;
    
    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate TIS employee IDs for new employees
CREATE OR REPLACE FUNCTION set_employee_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.employee_id IS NULL OR NEW.employee_id = '' THEN
        NEW.employee_id := generate_employee_id();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_set_employee_id ON employees;

-- Create the trigger
CREATE TRIGGER trigger_set_employee_id
    BEFORE INSERT ON employees
    FOR EACH ROW
    EXECUTE FUNCTION set_employee_id();

-- Add comments for documentation
COMMENT ON COLUMN employees.employee_id IS 'Employee PS Number in TIS00XXX format';
COMMENT ON FUNCTION generate_employee_id() IS 'Generates unique TIS employee IDs';
COMMENT ON FUNCTION set_employee_id() IS 'Trigger function to auto-generate employee IDs';

-- Log the migration completion
INSERT INTO migration_log (migration_name, executed_at, description) 
VALUES (
    '07_update_employee_ids_to_ps_numbers', 
    NOW(), 
    'Updated all employee IDs from EMP format to TIS (PS Number) format'
) ON CONFLICT DO NOTHING;
