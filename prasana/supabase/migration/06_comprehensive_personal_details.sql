-- =====================================================
-- COMPREHENSIVE PERSONAL DETAILS MIGRATION
-- Adds extensive personal information fields to HRMS
-- =====================================================

-- Add comprehensive personal detail columns to employees table
ALTER TABLE employees 
ADD COLUMN IF NOT EXISTS permanent_address_line1 TEXT,
ADD COLUMN IF NOT EXISTS permanent_address_line2 TEXT,
ADD COLUMN IF NOT EXISTS permanent_city VARCHAR(100),
ADD COLUMN IF NOT EXISTS permanent_state VARCHAR(100),
ADD COLUMN IF NOT EXISTS permanent_postal_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS permanent_country VARCHAR(100),

-- Additional Personal Details
ADD COLUMN IF NOT EXISTS blood_group VARCHAR(10),
ADD COLUMN IF NOT EXISTS religion VARCHAR(50),
ADD COLUMN IF NOT EXISTS caste VARCHAR(50),
ADD COLUMN IF NOT EXISTS category VARCHAR(50), -- General, OBC, SC, ST, etc.
ADD COLUMN IF NOT EXISTS physically_challenged BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS identification_marks TEXT,

-- Family Information
ADD COLUMN IF NOT EXISTS father_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS mother_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS spouse_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS spouse_occupation VARCHAR(100),
ADD COLUMN IF NOT EXISTS spouse_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS children_count INTEGER DEFAULT 0,

-- Additional Contact Information
ADD COLUMN IF NOT EXISTS alternate_phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS alternate_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS linkedin_profile VARCHAR(255),

-- Bank Details
ADD COLUMN IF NOT EXISTS bank_name VARCHAR(100),
ADD COLUMN IF NOT EXISTS bank_account_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS bank_ifsc_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS bank_branch VARCHAR(100),
ADD COLUMN IF NOT EXISTS pan_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS aadhar_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS passport_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS passport_expiry_date DATE,
ADD COLUMN IF NOT EXISTS driving_license_number VARCHAR(50),
ADD COLUMN IF NOT EXISTS driving_license_expiry_date DATE,

-- Professional Details
ADD COLUMN IF NOT EXISTS years_of_experience DECIMAL(4,2),
ADD COLUMN IF NOT EXISTS previous_company VARCHAR(100),
ADD COLUMN IF NOT EXISTS previous_designation VARCHAR(100),
ADD COLUMN IF NOT EXISTS previous_salary DECIMAL(12,2),
ADD COLUMN IF NOT EXISTS notice_period_days INTEGER,

-- Skills and Languages
ADD COLUMN IF NOT EXISTS technical_skills TEXT[], -- Array of skills
ADD COLUMN IF NOT EXISTS soft_skills TEXT[], -- Array of soft skills
ADD COLUMN IF NOT EXISTS languages_known TEXT[], -- Array of languages
ADD COLUMN IF NOT EXISTS certifications TEXT[], -- Array of certifications

-- Personal Interests
ADD COLUMN IF NOT EXISTS hobbies TEXT[],
ADD COLUMN IF NOT EXISTS interests TEXT[],
ADD COLUMN IF NOT EXISTS achievements TEXT[],

-- Health Information
ADD COLUMN IF NOT EXISTS medical_conditions TEXT,
ADD COLUMN IF NOT EXISTS allergies TEXT;

-- Employee Education History
CREATE TABLE IF NOT EXISTS employee_education (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    institution_name VARCHAR(200) NOT NULL,
    degree VARCHAR(100) NOT NULL,
    field_of_study VARCHAR(100),
    start_year INTEGER,
    end_year INTEGER,
    percentage_or_cgpa VARCHAR(20),
    board_or_university VARCHAR(200),
    location VARCHAR(100),
    is_completed BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Work Experience
CREATE TABLE IF NOT EXISTS employee_work_experience (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    company_name VARCHAR(200) NOT NULL,
    designation VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    is_current BOOLEAN DEFAULT false,
    salary DECIMAL(12,2),
    location VARCHAR(100),
    job_description TEXT,
    reason_for_leaving TEXT,
    supervisor_name VARCHAR(100),
    supervisor_contact VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Family Members
CREATE TABLE IF NOT EXISTS employee_family_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    relationship VARCHAR(50) NOT NULL, -- Father, Mother, Spouse, Child, Sibling, etc.
    date_of_birth DATE,
    occupation VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(255),
    is_dependent BOOLEAN DEFAULT false,
    is_emergency_contact BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Certifications
CREATE TABLE IF NOT EXISTS employee_certifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    certification_name VARCHAR(200) NOT NULL,
    issuing_organization VARCHAR(200) NOT NULL,
    issue_date DATE,
    expiry_date DATE,
    credential_id VARCHAR(100),
    credential_url TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee Languages
CREATE TABLE IF NOT EXISTS employee_languages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    language VARCHAR(50) NOT NULL,
    proficiency VARCHAR(20) NOT NULL, -- Beginner, Intermediate, Advanced, Native
    can_read BOOLEAN DEFAULT false,
    can_write BOOLEAN DEFAULT false,
    can_speak BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Employee References
CREATE TABLE IF NOT EXISTS employee_references (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    designation VARCHAR(100),
    company VARCHAR(200),
    relationship VARCHAR(100), -- Ex-Manager, Colleague, Professor, etc.
    phone VARCHAR(20),
    email VARCHAR(255),
    years_known INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_employee_education_employee_id ON employee_education(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_work_experience_employee_id ON employee_work_experience(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_family_members_employee_id ON employee_family_members(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_certifications_employee_id ON employee_certifications(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_languages_employee_id ON employee_languages(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_references_employee_id ON employee_references(employee_id);

-- Add comments for documentation
COMMENT ON TABLE employee_education IS 'Stores educational qualifications and academic history of employees';
COMMENT ON TABLE employee_work_experience IS 'Stores previous work experience and employment history';
COMMENT ON TABLE employee_family_members IS 'Stores family member information including dependents and emergency contacts';
COMMENT ON TABLE employee_certifications IS 'Stores professional certifications and credentials';
COMMENT ON TABLE employee_languages IS 'Stores language proficiency information';
COMMENT ON TABLE employee_references IS 'Stores professional and personal references';
