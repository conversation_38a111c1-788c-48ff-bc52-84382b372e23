# PS Number (TIS Code) Conversion Guide

## Overview
This document outlines the conversion from old Employee IDs (EMP format) to PS Numbers (TIS format) in the HRMS system.

## PS Number Format
- **Format**: `TIS00XXX` (e.g., TIS00003, TIS00010)
- **Structure**: 
  - `TIS` = Prefix (TechnoSprint Identifier)
  - `00XXX` = 5-digit number with leading zeros

## Employee PS Number Mapping

### 👑 Leadership Team
| Name | PS Number | Role |
|------|-----------|------|
| Arun G | TIS00003 | CEO |
| <PERSON><PERSON><PERSON> Prasadh R | TIS00012 | SDM (NEXUS) |
| Yuvaraj S | TIS00011 | SDM (DYNAMIX) |
| Sri Ram S | TIS00013 | SDM (ATHENA) |
| Aamina Begam T | TIS00018 | SDM (TITAN) |

### 🔧 Technical & Client Managers
| Name | PS Number | Role | Team |
|------|-----------|------|------|
| Selvendrane K | TIS00020 | TDM | ATHENA |
| Purushoth A | TIS00030 | TDM | DYNAMIX |
| <PERSON> | TIS00019 | TDM | NEXUS |
| Kollati Gowtham Venkata Bhaskar | TIS00024 | TDM | TITAN |
| Maheshwaran S | TIS00017 | CXM | ATHENA |
| Kiyshor K | TIS00025 | CXM | DYNAMIX |
| Darshan K | TIS00010 | CXM | NEXUS |
| Prasanna Venkatesh B | TIS00016 | CXM | TITAN |

### 👥 Team Members
| Name | PS Number | Team |
|------|-----------|------|
| Fazeela M | TIS00031 | ATHENA |
| Sivarajani K | TIS00033 | ATHENA |
| Dommeti Praveen Satya Prakesh | TIS00023 | DYNAMIX |
| Nitesh S | TIS00034 | DYNAMIX |
| Nithish K | TIS00038 | DYNAMIX |
| Sakthivel N | TIS00036 | NEXUS |
| Gaushik | TIS00040 | NEXUS |
| Hariharan B | TIS00037 | NEXUS |
| Yamini Selvam | TIS00035 | TITAN |
| S. Keerthipriya | TIS00032 | TITAN |
| Shri Mathi | TIS00039 | TITAN |

### 🚪 Left Members
| Name | PS Number | Previous Team | Status |
|------|-----------|---------------|--------|
| Afra M | TIS00014 | NEXUS | Inactive |
| Najla M H | TIS00015 | ATHENA | Inactive |
| Rakesh S | TIS00021 | ATHENA | Inactive |
| Deepak S | TIS00026 | DYNAMIX | Inactive |
| Kanimozhi V | TIS00028 | TITAN | Inactive |
| Mohamed Rizwan R | TIS00027 | TITAN | Inactive |
| Theepatharan | TIS00022 | ATHENA | Inactive |

## Implementation Steps

### 1. Database Migration
Run the migration script:
```sql
-- Execute: prasana/supabase/migration/07_update_employee_ids_to_ps_numbers.sql
```

### 2. Frontend Updates
The following components have been updated to handle PS Numbers:

#### HR Dashboard (`HRDashboard.tsx`)
- ✅ Column header changed from "Employee ID" to "PS Number"
- ✅ PS Numbers displayed with monospace font and blue color
- ✅ Inline editing with TIS format validation
- ✅ Input validation with pattern matching
- ✅ Left members table shows PS Numbers

#### HRMS Service (`hrmsService.ts`)
- ✅ New employee creation uses TIS format
- ✅ PS Number validation function added
- ✅ Duplicate PS Number checking
- ✅ Auto-generation of sequential TIS numbers

#### Data Fetching (`supabaseHR.ts`)
- ✅ Fallback query for direct employee data access
- ✅ Proper PS Number field mapping

### 3. Browser Console Conversion
For immediate conversion of existing data, use the browser console:

```javascript
// Check current employee ID formats
await checkEmployeeIdFormats();

// Convert all EMP format IDs to TIS format
await updateEmployeeIdsToPSNumbers();
```

## Validation Rules

### PS Number Format Validation
- **Pattern**: `^TIS\d{5}$`
- **Examples**: 
  - ✅ Valid: TIS00003, TIS00010, TIS00040
  - ❌ Invalid: EMP001, TIS3, TIS0003, tis00003

### Input Validation
- Automatic uppercase conversion
- Real-time format validation
- Duplicate checking before save
- Error messages for invalid formats

## Features Added

### 🎨 Visual Enhancements
- PS Numbers displayed with monospace font
- Blue color coding for PS Numbers
- Visual validation feedback (red border for invalid format)
- Consistent styling across active and left member tables

### 🔒 Data Integrity
- Duplicate PS Number prevention
- Format validation on input
- Database constraints for TIS format
- Automatic ID generation for new employees

### 🔄 Migration Safety
- Fallback data fetching if RPC functions unavailable
- Gradual conversion support
- Existing data preservation
- Error handling and logging

## Testing Checklist

### ✅ HR Dashboard
- [ ] PS Numbers display correctly in table
- [ ] Inline editing works with validation
- [ ] New format validation shows errors
- [ ] Save functionality works with TIS format
- [ ] Left members table shows PS Numbers

### ✅ Employee Creation
- [ ] New employees get TIS format IDs
- [ ] Sequential numbering works correctly
- [ ] No duplicate PS Numbers created

### ✅ Data Migration
- [ ] Existing EMP IDs converted to TIS
- [ ] All team members have correct PS Numbers
- [ ] Left members marked as inactive
- [ ] No data loss during conversion

## Troubleshooting

### Common Issues
1. **"PS Number must be in TIS00XXX format"**
   - Ensure format is exactly TIS followed by 5 digits
   - Use uppercase TIS prefix

2. **"PS Number already exists"**
   - Check for duplicate assignments
   - Use the next available number

3. **Data not loading**
   - Check if migration script was executed
   - Verify database connection
   - Check browser console for errors

### Support
For issues with PS Number conversion:
1. Check browser console for error messages
2. Verify database migration completion
3. Test with the conversion script functions
4. Contact system administrator if problems persist

## Next Steps
1. Execute database migration
2. Test PS Number functionality
3. Train users on new PS Number system
4. Monitor for any conversion issues
5. Update documentation as needed
